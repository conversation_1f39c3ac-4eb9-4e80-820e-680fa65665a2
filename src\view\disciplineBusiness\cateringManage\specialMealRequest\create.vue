<template>
  <div class="special-meal-create">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <Icon type="md-restaurant" size="28" color="#1890ff" />
        <h2 class="page-title">特殊餐申请登记</h2>
        <div class="header-divider"></div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <Row :gutter="24" class="content-row">
        <!-- 左侧人员选择区域 -->
        <Col :span="7" class="left-panel">
          <div class="panel-card">
            <div class="card-header">
              <Icon type="ios-people" size="20" color="#1890ff" />
              <span class="card-title">选择人员</span>
            </div>
            <div class="card-content">
              <ChoosePeople @selectUser="selectUser" :selectedIds="choosenList.map(item => { return item.jgrybm }).join(',')">
              </ChoosePeople>

              <!-- 已选人员列表 -->
              <div class="selected-people" v-if="choosenList.length > 0">
                <div class="people-header">
                  <span class="people-count">已选择 {{ choosenList.length }} 人</span>
                </div>
                <div class="people-list">
                  <div
                    v-for="(person, index) in choosenList"
                    :key="person.jgrybm"
                    class="person-item"
                  >
                    <div class="person-info">
                      <div class="person-name">{{ person.jgryxm }}</div>
                      <div class="person-room">{{ person.roomName }}</div>
                    </div>
                    <Button
                      type="error"
                      ghost
                      size="small"
                      icon="ios-close"
                      @click="removeChoosenPeople(index)"
                      class="remove-btn"
                    >
                      移除
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Col>

        <!-- 右侧表单区域 -->
        <Col :span="17" class="right-panel">
          <div class="panel-card">
            <div class="card-header">
              <Icon type="ios-document" size="20" color="#1890ff" />
              <span class="card-title">申请信息</span>
            </div>
            <div class="card-content form-content">
              <Form :model="formData" ref="form" :rules="ruleValidate" :label-width="140" label-position="left"
                @submit.native.prevent class="meal-form">

                <!-- 配餐类型 -->
                <div class="form-section">
                  <FormItem label="配餐类型" prop="mealType" class="form-item">
                    <s-dicgrid
                      v-model="formData.mealType"
                      dicName="ZD_PCGL_PCLX"
                      :isSearch="false"
                      :multiple="false"
                      placeholder="请选择配餐类型"
                    />
                  </FormItem>
                </div>

                <!-- 配餐时间 -->
                <div class="form-section">
                  <FormItem label="配餐时间" prop="mealTime" class="form-item">
                    <DatePicker
                      type="daterange"
                      v-model="formData.mealTime"
                      placeholder="选择配餐时间范围"
                      style="width: 100%"
                      format="yyyy-MM-dd"
                      @on-change="handleMealTimeChange"
                      class="date-picker"
                    />
                  </FormItem>
                </div>

                <!-- 用餐时段 -->
                <div class="form-section">
                  <FormItem label="用餐时段" prop="mealPeriod" class="form-item">
                    <div class="checkbox-group-wrapper">
                      <CheckboxGroup v-model="formData.mealPeriod" class="meal-period-group">
                        <Checkbox
                          v-for="item in mealPeriodOptions"
                          :key="item.code"
                          :label="item.code"
                          class="meal-period-item"
                        >
                          <span class="checkbox-label">{{ item.name }}</span>
                        </Checkbox>
                      </CheckboxGroup>
                    </div>
                  </FormItem>
                </div>

                <!-- 指定日期 -->
                <div class="form-section">
                  <FormItem label="指定日期" prop="specifiedDateType" class="form-item">
                    <div class="date-selection">
                      <div class="date-type-section">
                        <label class="section-label">日期类型</label>
                        <RadioGroup v-model="formData.specifiedDateType" class="date-type-group">
                          <Radio
                            v-for="item in useTimeTypeList"
                            :label="item.code"
                            :key="item.code"
                            class="date-type-item"
                          >
                            {{ item.name }}
                          </Radio>
                        </RadioGroup>
                      </div>

                      <div class="weeks-section">
                        <label class="section-label">选择星期</label>
                        <CheckboxGroup v-model="formData.specifiedDateTime" class="weeks-group">
                          <Checkbox
                            v-for="item in useWeeksList"
                            :label="item.code"
                            :key="item.code"
                            class="week-item"
                          >
                            {{ item.name }}
                          </Checkbox>
                        </CheckboxGroup>
                      </div>
                    </div>
                  </FormItem>
                </div>

                <!-- 申请原因 -->
                <div class="form-section">
                  <FormItem label="申请原因" prop="reason" class="form-item">
                    <Input
                      v-model="formData.reason"
                      type="textarea"
                      :autosize="{ minRows: 4, maxRows: 8 }"
                      placeholder="请详细说明申请特殊餐的原因..."
                      class="reason-textarea"
                    />
                  </FormItem>
                </div>
              </Form>
            </div>
          </div>
        </Col>
      </Row>
    </div>

    <!-- 底部操作按钮 -->
    <div class="footer-actions">
      <div class="actions-container">
        <Button size="large" @click="handleCancel" class="cancel-btn">
          <Icon type="ios-arrow-back" />
          取消
        </Button>
        <Button size="large" type="primary" :loading="loading" @click="handleSubmit" class="submit-btn">
          <Icon type="ios-checkmark" />
          确认申请
        </Button>
      </div>
    </div>
  </div>
</template>

<script>
import { ChoosePeople } from "./components";
import { mapActions } from "vuex";
import dayjs from 'dayjs';

export default {
  components: {
    ChoosePeople,
  },
  data() {
    return {
      formData: {
        mealType: "",
        mealTime: [],
        mealPeriod: [],
        specifiedDateType: "",
        specifiedDateTime: [],
        reason: "",
      },
      choosenList: [],
      mealPeriodOptions: [],
      useTimeTypeList: [],
      useWeeksList: [],
      ruleValidate: {
        mealType: [
          {
            required: true,
            message: "请选择配餐类型",
            trigger: "change",
          },
        ],
        mealTime: [
          {
            required: true,
            type: "array",
            message: "请选择配餐时间",
            trigger: "change",
          },
        ],
        mealPeriod: [
          {
            required: true,
            type: "array",
            message: "请选择用餐时段",
            trigger: "change",
          },
        ],
        reason: [
          {
            required: true,
            message: "请输入申请原因",
            trigger: "blur",
          },
        ],
      },
      loading: false,
    };
  },
  created() {
    this.getMealPeriodOptions();
    this.getTimeTypeOptions();
    this.getWeeksOptions();
  },
  watch: {
    'formData.specifiedDateType': {
      handler(newV) {
        if (newV == 1) {
          this.formData.specifiedDateTime = ['1', '2', '3', '4', '5', '6', '7']
        } else {
          this.formData.specifiedDateTime = ['1', '2', '3', '4', '5',]
        }
      }
    }
  },
  methods: {
    ...mapActions(["authGetRequest", "authPostRequest"]),
    
    // 获取用餐时段选项
    getMealPeriodOptions() {
      this.authGetRequest({ url: "/bsp-com/static/dic/pam/ZD_PCGL_DSLX.js" }).then(res => {
        let useMeal = eval('(' + res + ')')
        this.mealPeriodOptions = useMeal()
      })
    },

    // 获取指定日期类型选项
    getTimeTypeOptions() {
      this.authGetRequest({ url: "/bsp-com/static/dic/pam/ZD_PCGL_ZDRQLX.js" }).then(res => {
        let useTime = eval('(' + res + ')')
        this.useTimeTypeList = useTime()
      })
    },

    // 获取星期选项
    getWeeksOptions() {
      this.useWeeksList = [
        {
          name: "周一",
          code: '1'
        },
        {
          name: "周二",
          code: '2'
        },
        {
          name: "周三",
          code: '3'
        },
        {
          name: "周四",
          code: '4'
        },
        {
          name: "周五",
          code: '5'
        },
        {
          name: "周六",
          code: '6'
        },
        {
          name: "周日",
          code: '7'
        }
      ];
    },
    
    selectUser(data) {
      const uniqueArray = new Set(data.map(item => JSON.stringify(item)));
      const resultArray = Array.from(uniqueArray).map(item => JSON.parse(item));
      this.choosenList = resultArray;
    },
    
    removeChoosenPeople(index) {
      this.choosenList.splice(index, 1);
    },
    
    handleMealTimeChange(dates) {
      this.formData.mealTime = dates;
    },
    
    handleSpecifiedDateTypeChange() {
      this.formData.specifiedDateTime = [];
    },
    
    handleCancel() {
      this.$refs.form.resetFields();
      this.choosenList = [];
      this.$router.replace({ name: "specialMealRequest" });
    },
    
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid && this.choosenList.length) {
          this.loading = true;
          
          // 构建批量保存的参数
          const batchReqVO = {
            jgrybInfo: this.choosenList.map(item => ({
              jgrybm: item.jgrybm,
              roomId: item.roomId
            })),
            mealType: this.formData.mealType,
            mealStartTime: this.formData.mealTime[0] ? dayjs(this.formData.mealTime[0]).format('YYYY-MM-DD') : '',
            mealEndTime: this.formData.mealTime[1] ? dayjs(this.formData.mealTime[1]).format('YYYY-MM-DD') : '',
            mealPeriod: this.formData.mealPeriod.join(','),
            specifiedDateType: this.formData.specifiedDateType,
            specifiedDate: this.formData.specifiedDateTime.join(','),
            reason: this.formData.reason
          };

          this.authPostRequest({
            url: this.$path.specialApply_createBatch,
            params: batchReqVO,
          }).then((res) => {
            this.loading = false;
            if (res.success) {
              this.$Message.success("特殊餐申请成功");
              this.$refs.form.resetFields();
              this.choosenList = [];
              this.$router.replace({ name: "specialMealRequest" });
            } else {
              this.$Message.error(res.msg || "申请失败");
            }
          }).catch(() => {
            this.loading = false;
            this.$Message.error("申请失败");
          });
        } else {
          this.$Message.error(
            "表单验证失败，请确定选择了监管人员与填写了必填表单。"
          );
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
@import "~@/assets/style/formInfo.css";

.special-meal-create {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 0;

  // 页面标题
  .page-header {
    background: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 24px;

    .header-content {
      padding: 20px 24px;
      display: flex;
      align-items: center;

      .page-title {
        margin: 0 0 0 12px;
        font-size: 24px;
        font-weight: 600;
        color: #262626;
        flex: 1;
      }

      .header-divider {
        width: 4px;
        height: 32px;
        background: linear-gradient(135deg, #1890ff, #40a9ff);
        border-radius: 2px;
        margin-left: auto;
      }
    }
  }

  // 主要内容区域
  .main-content {
    padding: 0 24px;

    .content-row {
      min-height: calc(100vh - 200px);
    }
  }

  // 面板卡片
  .panel-card {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;

    .card-header {
      background: linear-gradient(135deg, #1890ff, #40a9ff);
      color: #fff;
      padding: 16px 20px;
      display: flex;
      align-items: center;

      .card-title {
        margin-left: 8px;
        font-size: 16px;
        font-weight: 600;
      }
    }

    .card-content {
      flex: 1;
      padding: 24px;
      overflow-y: auto;
    }
  }

  // 左侧面板
  .left-panel {
    .selected-people {
      margin-top: 20px;

      .people-header {
        padding: 12px 16px;
        background: #f8f9fa;
        border-radius: 8px;
        margin-bottom: 12px;

        .people-count {
          font-size: 14px;
          font-weight: 600;
          color: #1890ff;
        }
      }

      .people-list {
        max-height: 400px;
        overflow-y: auto;

        .person-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 12px 16px;
          background: #fff;
          border: 1px solid #e8e8e8;
          border-radius: 8px;
          margin-bottom: 8px;
          transition: all 0.3s ease;

          &:hover {
            border-color: #1890ff;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
          }

          .person-info {
            flex: 1;

            .person-name {
              font-size: 14px;
              font-weight: 600;
              color: #262626;
              margin-bottom: 4px;
            }

            .person-room {
              font-size: 12px;
              color: #8c8c8c;
            }
          }

          .remove-btn {
            border-color: #ff4d4f;
            color: #ff4d4f;

            &:hover {
              background: #ff4d4f;
              color: #fff;
            }
          }
        }
      }
    }
  }

  // 右侧表单
  .right-panel {
    .form-content {
      .meal-form {
        .form-section {
          margin-bottom: 32px;

          &:last-child {
            margin-bottom: 0;
          }
        }

        .form-item {
          margin-bottom: 0;

          /deep/ .ivu-form-item-label {
            font-weight: 600;
            color: #262626;
            font-size: 14px;
            line-height: 1.5;
          }

          /deep/ .ivu-form-item-content {
            .ivu-input,
            .ivu-select,
            .ivu-date-picker {
              border-radius: 8px;
              border-color: #d9d9d9;

              &:hover {
                border-color: #40a9ff;
              }

              &:focus {
                border-color: #1890ff;
                box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
              }
            }
          }
        }

        // 复选框组样式
        .checkbox-group-wrapper {
          .meal-period-group {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;

            .meal-period-item {
              background: #f8f9fa;
              border: 1px solid #e8e8e8;
              border-radius: 8px;
              padding: 8px 16px;
              transition: all 0.3s ease;

              &:hover {
                border-color: #1890ff;
                background: #e6f7ff;
              }

              /deep/ .ivu-checkbox-checked {
                .ivu-checkbox-inner {
                  background-color: #1890ff;
                  border-color: #1890ff;
                }
              }

              .checkbox-label {
                margin-left: 8px;
                font-weight: 500;
              }
            }
          }
        }

        // 日期选择样式
        .date-selection {
          .date-type-section,
          .weeks-section {
            margin-bottom: 20px;

            &:last-child {
              margin-bottom: 0;
            }

            .section-label {
              display: block;
              font-size: 13px;
              font-weight: 600;
              color: #595959;
              margin-bottom: 12px;
            }
          }

          .date-type-group {
            display: flex;
            gap: 16px;

            .date-type-item {
              background: #f8f9fa;
              border: 1px solid #e8e8e8;
              border-radius: 8px;
              padding: 8px 16px;
              transition: all 0.3s ease;

              &:hover {
                border-color: #1890ff;
                background: #e6f7ff;
              }

              /deep/ .ivu-radio-checked {
                .ivu-radio-inner {
                  background-color: #1890ff;
                  border-color: #1890ff;
                }
              }
            }
          }

          .weeks-group {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;

            .week-item {
              background: #f8f9fa;
              border: 1px solid #e8e8e8;
              border-radius: 8px;
              padding: 8px 12px;
              text-align: center;
              transition: all 0.3s ease;

              &:hover {
                border-color: #1890ff;
                background: #e6f7ff;
              }

              /deep/ .ivu-checkbox-checked {
                .ivu-checkbox-inner {
                  background-color: #1890ff;
                  border-color: #1890ff;
                }
              }
            }
          }
        }

        // 文本域样式
        .reason-textarea {
          /deep/ .ivu-input {
            border-radius: 8px;
            border-color: #d9d9d9;
            font-size: 14px;
            line-height: 1.6;

            &:hover {
              border-color: #40a9ff;
            }

            &:focus {
              border-color: #1890ff;
              box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            }
          }
        }
      }
    }
  }

  // 底部操作按钮
  .footer-actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    border-top: 1px solid #e8e8e8;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
    z-index: 1000;

    .actions-container {
      padding: 16px 24px;
      display: flex;
      justify-content: center;
      gap: 16px;

      .cancel-btn,
      .submit-btn {
        min-width: 120px;
        height: 40px;
        border-radius: 8px;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 6px;

        /deep/ .ivu-icon {
          font-size: 16px;
        }
      }

      .cancel-btn {
        border-color: #d9d9d9;
        color: #595959;

        &:hover {
          border-color: #40a9ff;
          color: #1890ff;
        }
      }

      .submit-btn {
        background: linear-gradient(135deg, #1890ff, #40a9ff);
        border-color: #1890ff;

        &:hover {
          background: linear-gradient(135deg, #40a9ff, #1890ff);
          box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
      }
    }
  }
}

// 全局样式覆盖
/deep/ .personnel-select-area {
  border: 2px dashed #d9d9d9;
  background: #fafafa;
  border-radius: 12px;
  transition: all 0.3s ease;

  &:hover {
    border-color: #1890ff;
    background: #e6f7ff;
  }

  .personnel-select-icon {
    background: #e6f7ff;
    border-radius: 8px;

    .ivu-icon {
      color: #1890ff;
    }
  }

  .personnel-select-text {
    color: #595959;
    font-weight: 500;
  }
}
</style>
