<template>
    <div class="special-meal-detail">
        <!-- 页面头部 -->
        <div class="detail-header">
            <div class="header-left">
                <Icon type="ios-document-outline" size="20" color="#2b5fd9" />
                <h3 class="header-title">特殊配餐申请详情</h3>
            </div>
            <div class="header-actions">
                <Button type="text" icon="md-close" @click="back">关闭</Button>
            </div>
        </div>

        <!-- 页面内容 -->
        <div class="detail-content">
            <!-- 人员信息卡片 -->
            <div class="info-card">
                <div class="card-header">
                    <div class="header-left">
                        <Icon type="ios-person" size="16" color="#2b5fd9" />
                        <span class="card-title">人员信息</span>
                    </div>
                </div>
                <div class="card-content">
                    <div class="person-info-layout">
                        <div class="person-photo">
                            <div class="photo-container">
                                <img :src="ryxxInfo.frontPhoto ? ryxxInfo.frontPhoto : personImg" alt="人员照片">
                            </div>
                        </div>
                        <div class="person-details">
                            <Row :gutter="16" class="info-row">
                                <Col span="8">
                                    <div class="info-item">
                                        <span class="info-label">被监管人员</span>
                                        <span class="info-value">{{ ryxxInfo.bm || '-' }}</span>
                                    </div>
                                </Col>
                                <Col span="8">
                                    <div class="info-item">
                                        <span class="info-label">监室号</span>
                                        <span class="info-value">{{ ryxxInfo.jsh || '-' }}</span>
                                    </div>
                                </Col>
                                <Col span="8">
                                    <div class="info-item">
                                        <span class="info-label">入所时间</span>
                                        <span class="info-value">{{ ryxxInfo.rssj || '-' }}</span>
                                    </div>
                                </Col>
                            </Row>
                            <Row :gutter="16" class="info-row">
                                <Col span="8">
                                    <div class="info-item">
                                        <span class="info-label">诉讼环节</span>
                                        <span class="info-value">{{ ryxxInfo.sshjName || '-' }}</span>
                                    </div>
                                </Col>
                                <Col span="8">
                                    <div class="info-item">
                                        <span class="info-label">关押期限</span>
                                        <span class="info-value">{{ ryxxInfo.gyqx || '-' }}</span>
                                    </div>
                                </Col>
                                <Col span="8">
                                    <div class="info-item">
                                        <span class="info-label">涉嫌罪名</span>
                                        <span class="info-value">{{ ryxxInfo.sxzm || '-' }}</span>
                                    </div>
                                </Col>
                            </Row>
                            <Row :gutter="16" class="info-row">
                                <Col span="8">
                                    <div class="info-item">
                                        <span class="info-label">风险等级</span>
                                        <span class="info-value" :class="getRiskLevelClass(ryxxInfo.fxdj)">{{ ryxxInfo.fxdj || '-' }}</span>
                                    </div>
                                </Col>
                                <Col span="8">
                                    <div class="info-item">
                                        <span class="info-label">户籍地</span>
                                        <span class="info-value">{{ ryxxInfo.hjd || '-' }}</span>
                                    </div>
                                </Col>
                                <Col span="8">
                                    <div class="info-item">
                                        <span class="info-label">近期就诊时间</span>
                                        <span class="info-value">{{ ryxxInfo.jqjzsj || '-' }}</span>
                                    </div>
                                </Col>
                            </Row>
                            <Row :gutter="16" class="info-row" v-if="ryxxInfo.zjzdbq">
                                <Col span="24">
                                    <div class="info-item">
                                        <span class="info-label">诊断病情</span>
                                        <span class="info-value">{{ ryxxInfo.zjzdbq || '-' }}</span>
                                    </div>
                                </Col>
                            </Row>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 申请登记卡片 -->
            <div class="info-card">
                <div class="card-header">
                    <div class="header-left">
                        <Icon type="ios-clipboard" size="16" color="#2b5fd9" />
                        <span class="card-title">申请登记</span>
                    </div>
                </div>
                <div class="card-content">
                    <Row :gutter="16" class="info-row">
                        <Col span="12">
                            <div class="info-item">
                                <span class="info-label">配餐类型</span>
                                <span class="info-value">{{ specialApplydetail.mealTypeName || '-' }}</span>
                            </div>
                        </Col>
                        <Col span="12">
                            <div class="info-item">
                                <span class="info-label">配餐时间</span>
                                <span class="info-value">
                                    {{ specialApplydetail.mealStartTime && specialApplydetail.mealEndTime
                                        ? `${specialApplydetail.mealStartTime} - ${specialApplydetail.mealEndTime}，共${specialApplydetail.daysDiff}天`
                                        : '-' }}
                                </span>
                            </div>
                        </Col>
                    </Row>
                    <Row :gutter="16" class="info-row">
                        <Col span="12">
                            <div class="info-item">
                                <span class="info-label">用餐时段</span>
                                <span class="info-value">{{ specialApplydetail.mealPeriodName || '-' }}</span>
                            </div>
                        </Col>
                        <Col span="12">
                            <div class="info-item">
                                <span class="info-label">指定日期</span>
                                <span class="info-value">{{ specialApplydetail.specifiedDateName || '-' }}</span>
                            </div>
                        </Col>
                    </Row>
                    <Row :gutter="16" class="info-row">
                        <Col span="24">
                            <div class="info-item full-width">
                                <span class="info-label">申请原因</span>
                                <span class="info-value reason-text">{{ specialApplydetail.reason || '-' }}</span>
                            </div>
                        </Col>
                    </Row>
                    <Row :gutter="16" class="info-row">
                        <Col span="12">
                            <div class="info-item">
                                <span class="info-label">申请人</span>
                                <span class="info-value">{{ specialApplydetail.regOperatorXm || '-' }}</span>
                            </div>
                        </Col>
                        <Col span="12">
                            <div class="info-item">
                                <span class="info-label">申请时间</span>
                                <span class="info-value">{{ specialApplydetail.regTime || '-' }}</span>
                            </div>
                        </Col>
                    </Row>
                </div>
            </div>
            <!-- 医生审批意见卡片 -->
            <div class="info-card">
                <div class="card-header">
                    <div class="header-left">
                        <Icon type="ios-medical" size="16" color="#2b5fd9" />
                        <span class="card-title">医生审批意见</span>
                    </div>
                </div>
                <div class="card-content">
                    <Row :gutter="16" class="info-row">
                        <Col span="12">
                            <div class="info-item">
                                <span class="info-label">审批结果</span>
                                <span class="info-value" :class="getApprovalResultClass(specialApplydetail.doctorApprovalResult)">
                                    {{ getApprovalResultText(specialApplydetail.doctorApprovalResult) }}
                                </span>
                            </div>
                        </Col>
                        <Col span="12">
                            <div class="info-item">
                                <span class="info-label">审批人</span>
                                <span class="info-value">{{ specialApplydetail.doctorApproverXm || '-' }}</span>
                            </div>
                        </Col>
                    </Row>
                    <Row :gutter="16" class="info-row">
                        <Col span="12">
                            <div class="info-item">
                                <span class="info-label">审批时间</span>
                                <span class="info-value">{{ specialApplydetail.doctorApproverTime || '-' }}</span>
                            </div>
                        </Col>
                        <Col span="12">
                            <div class="info-item">
                                <span class="info-label">审批意见</span>
                                <span class="info-value">{{ specialApplydetail.doctorApprovalComments || '-' }}</span>
                            </div>
                        </Col>
                    </Row>
                </div>
            </div>

            <!-- 所领导审批意见卡片 -->
            <div class="info-card">
                <div class="card-header">
                    <div class="header-left">
                        <Icon type="ios-checkmark-circle" size="16" color="#2b5fd9" />
                        <span class="card-title">所领导审批意见</span>
                    </div>
                </div>
                <div class="card-content">
                    <Row :gutter="16" class="info-row">
                        <Col span="12">
                            <div class="info-item">
                                <span class="info-label">审批结果</span>
                                <span class="info-value" :class="getApprovalResultClass(specialApplydetail.leaderApprovalResult)">
                                    {{ getApprovalResultText(specialApplydetail.leaderApprovalResult) }}
                                </span>
                            </div>
                        </Col>
                        <Col span="12">
                            <div class="info-item">
                                <span class="info-label">审批人</span>
                                <span class="info-value">{{ specialApplydetail.leaderApproverXm || '-' }}</span>
                            </div>
                        </Col>
                    </Row>
                    <Row :gutter="16" class="info-row">
                        <Col span="12">
                            <div class="info-item">
                                <span class="info-label">审批时间</span>
                                <span class="info-value">{{ specialApplydetail.leaderApproverTime || '-' }}</span>
                            </div>
                        </Col>
                        <Col span="12">
                            <div class="info-item">
                                <span class="info-label">审批意见</span>
                                <span class="info-value">{{ specialApplydetail.leaderApprovalComments || '-' }}</span>
                            </div>
                        </Col>
                    </Row>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { mapActions } from 'vuex'
import lineInfo from '../component/lineInfo.vue'
import dayjs from 'dayjs'
import Cookies from 'js-cookie'
export default {
    name: "specialMealDetail",
    data() {
        return {
            ryxxInfo: {},
            specialApplydetail: {},
            personImg: require("../../../../assets/images/cateringManage/default_Img.svg"),
            formItem: {
                doctorApprovalResult: "",
                doctorApprovalComments: "",
            },
            id: "",
        }
    },

    methods: {
        ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),

        // 获取人员信息
        handleGetRyxxInfo(selectUseIds) {
            this.authGetRequest({ url: this.$path.specialApply_people_search, params: { jgrybm: selectUseIds } }).then(res => {
                if (res.success) {
                    this.ryxxInfo = res.data
                }
            })
        },

        // 获取风险等级样式类
        getRiskLevelClass(level) {
            const levelMap = {
                '高风险': 'risk-high',
                '中风险': 'risk-medium',
                '低风险': 'risk-low'
            }
            return levelMap[level] || ''
        },

        // 获取审批结果文本
        getApprovalResultText(result) {
            if (result === 1) return '同意'
            if (result === 0) return '不同意'
            return '-'
        },

        // 获取审批结果样式类
        getApprovalResultClass(result) {
            if (result === 1) return 'approval-agree'
            if (result === 0) return 'approval-disagree'
            return ''
        },
        handleGetSpecialApplydetail(id) {
            this.authGetRequest({ url: this.$path.specialApply_people_detail, params: { id: id } }).then(res => {
                if (res.success) {
                    this.specialApplydetail = res.data
                    console.error(res.data, '[res.data]');

                    let mealPeriod = this.specialApplydetail.mealPeriod.split(',')
                    let specifiedDate = this.specialApplydetail.specifiedDate.split(',')
                    this.specialApplydetail.doctorApprovalResult
                    let mealPeriodName = mealPeriod.map(item => {
                        if (item == 0) {
                            item = '早餐'
                        } else if (item == 1) {
                            item = '午餐'
                        } else {
                            item = "晚餐"
                        }
                        return item
                    })
                    let specifiedDateName = specifiedDate.map(item => {
                        if (item == 1) {
                            item = '星期一'
                        } else if (item == 2) {
                            item = '星期二'
                        } else if (item == 3) {
                            item = '星期三'
                        } else if (item == 4) {
                            item = '星期四'
                        } else if (item == 5) {
                            item = '星期五'
                        } else if (item == 6) {
                            item = '星期六'
                        } else {
                            item = '星期日'
                        }
                        return item
                    })

                    let mealTypeName = ""
                    switch (this.specialApplydetail.mealType) {
                        case "00":
                            mealTypeName = '普通餐'
                            break;
                        case "01":
                            mealTypeName = '清真餐'
                            break;
                        case "02":
                            mealTypeName = '生日餐'
                            break;
                        case "03":
                            mealTypeName = '节日餐'
                            break;
                        case "04":
                            mealTypeName = '宗教餐'
                            break;
                        case "05":
                            mealTypeName = '低糖餐'
                            break;
                        case "06":
                            mealTypeName = '老年餐'
                            break;
                        case "07":
                            mealTypeName = '病号餐'
                            break;
                        default:
                            mealTypeName = '其他餐'
                            break;
                    }
                    this.specialApplydetail['mealTypeName'] = mealTypeName
                    this.specialApplydetail['mealPeriodName'] = mealPeriodName.join(',')
                    this.specialApplydetail['specifiedDateName'] = specifiedDateName.join(',')
                    const startDate = dayjs(this.specialApplydetail.mealStartTime);
                    const endDate = dayjs(this.specialApplydetail.mealEndTime);
                    const daysDiff = endDate.diff(startDate, 'day');
                    this.specialApplydetail['daysDiff'] = daysDiff
                }
            })
        },
        back() {
            window.close(); // 尝试关闭当前窗口
        }
    },

    components: {
        lineInfo
    },

    created() {
        const { jgrybm, id } = this.$route.query
        this.id = id
        this.handleGetRyxxInfo(jgrybm)
        this.handleGetSpecialApplydetail(id)
        this.doctorApproveName = Cookies.get('bsp_bus_user_login')
    },

    computed: {},

}

</script>

<style scoped lang="less">
// 引入系统变量
@import "~@/assets/style/variables.less";

.special-meal-detail {
    width: 100%;
    height: 100%;
    background: @background-color-light;
    display: flex;
    flex-direction: column;

    // 页面头部
    .detail-header {
        flex-shrink: 0;
        height: 60px;
        padding: 0 @padding-lg;
        background: @background-color-base;
        border-bottom: 1px solid @border-color-base;
        display: flex;
        align-items: center;
        justify-content: space-between;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);

        .header-left {
            display: flex;
            align-items: center;
            gap: 12px;

            .header-title {
                margin: 0;
                font-size: 18px;
                font-weight: 600;
                color: @text-color-primary;
            }
        }

        .header-actions {
            display: flex;
            gap: @spacing-sm;
        }
    }

    // 页面内容
    .detail-content {
        flex: 1;
        padding: @padding-lg;
        overflow-y: auto;
        display: flex;
        flex-direction: column;
        gap: @spacing-lg;

        // 信息卡片
        .info-card {
            background: @background-color-base;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            border: 1px solid #e8f4fd;
            overflow: hidden;

            // 卡片头部
            .card-header {
                padding: 20px 24px 16px;
                background: #fafbfc;
                border-bottom: 1px solid #f0f0f0;

                .header-left {
                    display: flex;
                    align-items: center;
                    gap: 8px;

                    .card-title {
                        font-size: 16px;
                        font-weight: 600;
                        color: @text-color-primary;
                        margin: 0;
                    }
                }
            }

            // 卡片内容
            .card-content {
                padding: 24px;
            }
        }

        // 人员信息布局
        .person-info-layout {
            display: flex;
            gap: 24px;
            align-items: flex-start;

            .person-photo {
                flex-shrink: 0;

                .photo-container {
                    width: 140px;
                    height: 140px;
                    border-radius: 8px;
                    overflow: hidden;
                    border: 2px solid #e8f4fd;
                    background: #f8f9fa;

                    img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                    }
                }
            }

            .person-details {
                flex: 1;
            }
        }

        // 信息行
        .info-row {
            margin-bottom: 16px;

            &:last-child {
                margin-bottom: 0;
            }
        }

        // 信息项
        .info-item {
            display: flex;
            align-items: flex-start;
            min-height: 40px;

            &.full-width {
                flex-direction: column;
                align-items: stretch;

                .info-label {
                    margin-bottom: 8px;
                }

                .info-value {
                    padding: 12px;
                    background: #f8f9fa;
                    border-radius: 6px;
                    border: 1px solid #e9ecef;
                    min-height: 60px;
                    line-height: 1.5;

                    &.reason-text {
                        white-space: pre-wrap;
                        word-break: break-word;
                    }
                }
            }

            .info-label {
                flex-shrink: 0;
                width: 120px;
                padding: 8px 12px;
                background: #e4eefc;
                color: @text-color-primary;
                font-weight: 500;
                border-radius: 4px 0 0 4px;
                display: flex;
                align-items: center;
                font-size: 14px;
            }

            .info-value {
                flex: 1;
                padding: 8px 12px;
                background: #f8f9fa;
                color: @text-color-secondary;
                border-radius: 0 4px 4px 0;
                display: flex;
                align-items: center;
                font-size: 14px;
                min-height: 40px;
                word-break: break-word;

                // 风险等级样式
                &.risk-high {
                    color: @error-color;
                    font-weight: 600;
                }

                &.risk-medium {
                    color: @warning-color;
                    font-weight: 600;
                }

                &.risk-low {
                    color: @success-color;
                    font-weight: 600;
                }

                // 审批结果样式
                &.approval-agree {
                    color: @success-color;
                    font-weight: 600;
                }

                &.approval-disagree {
                    color: @error-color;
                    font-weight: 600;
                }
            }
        }
    }

    // 响应式设计
    @media (max-width: 768px) {
        .detail-content {
            padding: @padding-md;
            gap: @spacing-md;
        }

        .person-info-layout {
            flex-direction: column;
            align-items: center;
            text-align: center;

            .person-photo {
                .photo-container {
                    width: 120px;
                    height: 120px;
                }
            }
        }

        .info-item {
            flex-direction: column;

            .info-label {
                width: 100%;
                border-radius: 4px 4px 0 0;
            }

            .info-value {
                border-radius: 0 0 4px 4px;
            }
        }
    }
}
</style>
